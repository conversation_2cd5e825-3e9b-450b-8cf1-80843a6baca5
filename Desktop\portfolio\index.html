<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meski Imane - Web Developer Portfolio</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">Portfolio</div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#about" class="nav-link">About</a></li>
                <li><a href="#skills" class="nav-link">Skills</a></li>
                <li><a href="#projects" class="nav-link">Projects</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <div class="hero-text">
                <p class="hero-greeting">Hello, I'm</p>
                <h1 class="hero-name">Meski Imane</h1>
                <h2 class="hero-title">Web Developer</h2>
                <p class="hero-description">
                    I create beautiful, responsive, and user-friendly websites and web applications
                    using modern technologies. Passionate about clean code and exceptional user experiences.
                </p>
                <div class="hero-buttons">
                    <a href="#projects" class="btn btn-primary">View My Work</a>
                    <a href="#contact" class="btn btn-outline">Get In Touch</a>
                </div>
                <div class="social-links">
                    <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                </div>
            </div>
            <div class="hero-image">
                <div class="profile-pic">
                    <i class="fas fa-user"></i>
                </div>
            </div>
        </div>
        <div class="scroll-indicator">
            <i class="fas fa-chevron-down"></i>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">About <span>Me</span></h2>
            <div class="about-content">
                <div class="about-text">
                    <h3>My Story</h3>
                    <p>
                        My journey into web development started during my university years when I built my first website.
                        What began as curiosity quickly turned into passion as I discovered the power of creating
                        digital experiences that can impact people's lives.
                    </p>
                    <p>
                        Over the years, I've had the privilege of working with diverse teams and clients,
                        from startups to established companies. Each project has taught me something new and
                        reinforced my belief that great software is built through collaboration and attention to detail.
                    </p>
                    <div class="stats">
                        <div class="stat">
                            <h4>50+</h4>
                            <p>Projects</p>
                        </div>
                        <div class="stat">
                            <h4>4+</h4>
                            <p>Years Experience</p>
                        </div>
                        <div class="stat">
                            <h4>20+</h4>
                            <p>Happy Clients</p>
                        </div>
                    </div>
                </div>
                <div class="about-skills">
                    <h3>What I Do</h3>
                    <div class="skill-item">
                        <i class="fas fa-code"></i>
                        <div>
                            <h4>Frontend Development</h4>
                            <p>Creating responsive and interactive user interfaces</p>
                        </div>
                    </div>
                    <div class="skill-item">
                        <i class="fas fa-server"></i>
                        <div>
                            <h4>Backend Development</h4>
                            <p>Building robust server-side applications and APIs</p>
                        </div>
                    </div>
                    <div class="skill-item">
                        <i class="fas fa-mobile-alt"></i>
                        <div>
                            <h4>Responsive Design</h4>
                            <p>Ensuring great user experience across all devices</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <h2 class="section-title">Skills & <span>Technologies</span></h2>
            <div class="skills-grid">
                <div class="skill-category">
                    <h3>Frontend</h3>
                    <div class="skill-tags">
                        <span class="skill-tag">HTML5</span>
                        <span class="skill-tag">CSS3</span>
                        <span class="skill-tag">JavaScript</span>
                        <span class="skill-tag">React</span>
                        <span class="skill-tag">Vue.js</span>
                        <span class="skill-tag">TypeScript</span>
                    </div>
                </div>
                <div class="skill-category">
                    <h3>Backend</h3>
                    <div class="skill-tags">
                        <span class="skill-tag">Node.js</span>
                        <span class="skill-tag">Express</span>
                        <span class="skill-tag">Python</span>
                        <span class="skill-tag">MongoDB</span>
                        <span class="skill-tag">PostgreSQL</span>
                        <span class="skill-tag">REST APIs</span>
                    </div>
                </div>
                <div class="skill-category">
                    <h3>Tools</h3>
                    <div class="skill-tags">
                        <span class="skill-tag">Git</span>
                        <span class="skill-tag">Docker</span>
                        <span class="skill-tag">AWS</span>
                        <span class="skill-tag">Figma</span>
                        <span class="skill-tag">VS Code</span>
                        <span class="skill-tag">Webpack</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <h2 class="section-title">My <span>Projects</span></h2>
            <div class="projects-grid">
                <div class="project-card">
                    <div class="project-image">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="project-content">
                        <h3>E-Commerce Platform</h3>
                        <p>A full-stack e-commerce solution with user authentication, payment integration, and admin dashboard.</p>
                        <div class="project-tech">
                            <span>React</span>
                            <span>Node.js</span>
                            <span>MongoDB</span>
                        </div>
                        <div class="project-links">
                            <a href="#" class="project-link"><i class="fas fa-external-link-alt"></i> Live Demo</a>
                            <a href="#" class="project-link"><i class="fab fa-github"></i> Code</a>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-image">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="project-content">
                        <h3>Task Management App</h3>
                        <p>A collaborative task management application with real-time updates and team collaboration features.</p>
                        <div class="project-tech">
                            <span>React</span>
                            <span>Firebase</span>
                            <span>Material-UI</span>
                        </div>
                        <div class="project-links">
                            <a href="#" class="project-link"><i class="fas fa-external-link-alt"></i> Live Demo</a>
                            <a href="#" class="project-link"><i class="fab fa-github"></i> Code</a>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-image">
                        <i class="fas fa-cloud-sun"></i>
                    </div>
                    <div class="project-content">
                        <h3>Weather Dashboard</h3>
                        <p>A responsive weather application with location-based forecasts and detailed weather analytics.</p>
                        <div class="project-tech">
                            <span>JavaScript</span>
                            <span>API</span>
                            <span>CSS3</span>
                        </div>
                        <div class="project-links">
                            <a href="#" class="project-link"><i class="fas fa-external-link-alt"></i> Live Demo</a>
                            <a href="#" class="project-link"><i class="fab fa-github"></i> Code</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Get In <span>Touch</span></h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Let's Connect</h3>
                    <p>I'm always open to discussing new opportunities and interesting projects.</p>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>+****************</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>New York, NY</span>
                    </div>
                </div>
                <form class="contact-form">
                    <div class="form-group">
                        <input type="text" placeholder="Your Name" required>
                    </div>
                    <div class="form-group">
                        <input type="email" placeholder="Your Email" required>
                    </div>
                    <div class="form-group">
                        <input type="text" placeholder="Subject" required>
                    </div>
                    <div class="form-group">
                        <textarea placeholder="Your Message" rows="5" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Send Message</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Meski Imane. Made with <i class="fas fa-heart"></i></p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
