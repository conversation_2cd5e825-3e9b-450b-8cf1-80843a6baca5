<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meski Imane - Developer Portfolio</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="profile-section">
                <div class="profile-image">
                    <i class="fas fa-user"></i>
                </div>
                <h2 class="profile-name">Meski Imane</h2>
                <p class="profile-title">Developer</p>
            </div>

            <nav class="sidebar-nav">
                <a href="#home" class="nav-item active">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
                <a href="#about" class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>About</span>
                </a>
                <a href="#projects" class="nav-item">
                    <i class="fas fa-folder"></i>
                    <span>Projects</span>
                </a>

                <a href="#stack" class="nav-item">
                    <i class="fas fa-layer-group"></i>
                    <span>Stack</span>
                </a>
                <a href="#contact" class="nav-item">
                    <i class="fas fa-envelope"></i>
                    <span>Contact</span>
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Home Section -->
            <section id="home" class="content-section active">
                <div class="status-indicator">
                    <span class="status-dot"></span>
                    Available for Work
                </div>

                <div class="hero-content">
                    <h1 class="hero-title">
                        Hey there!<br>
                        I'm <span class="highlight">Imane</span>...
                    </h1>
                    <p class="hero-description">
                        I develop websites.
                        I'm passionate about all things digital and eager to learn and grow.
                    </p>
                    <button class="cta-button">
                        More about Me
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>

                <div class="recent-projects">
                    <h2>Recent Projects</h2>
                    <div class="projects-grid">
                        <div class="project-card red">
                            <div class="project-content">
                                <h3>E-Commerce Platform</h3>
                                <p>Full-stack web application</p>
                            </div>
                        </div>
                        <div class="project-card blue">
                            <div class="project-content">
                                <h3>Task Manager</h3>
                                <p>React & Firebase app</p>
                            </div>
                        </div>
                        <div class="project-card purple">
                            <div class="project-content">
                                <h3>Portfolio Site</h3>
                                <p>Modern responsive design</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- About Section -->
            <section id="about" class="content-section">
                <h2 class="section-title">About Me</h2>
                <div class="about-content">
                    <p>
                        My journey into web development started during my university years when I built my first website.
                        What began as curiosity quickly turned into passion as I discovered the power of creating
                        digital experiences that can impact people's lives.
                    </p>
                    <p>
                        Over the years, I've had the privilege of working with diverse teams and clients,
                        from startups to established companies. Each project has taught me something new and
                        reinforced my belief that great software is built through collaboration and attention to detail.
                    </p>

                    <div class="skills-overview">
                        <h3>What I Do</h3>
                        <div class="skills-list">
                            <div class="skill-item">
                                <i class="fas fa-code"></i>
                                <div>
                                    <h4>Frontend Development</h4>
                                    <p>Creating responsive and interactive user interfaces</p>
                                </div>
                            </div>
                            <div class="skill-item">
                                <i class="fas fa-server"></i>
                                <div>
                                    <h4>Backend Development</h4>
                                    <p>Building robust server-side applications and APIs</p>
                                </div>
                            </div>
                            <div class="skill-item">
                                <i class="fas fa-mobile-alt"></i>
                                <div>
                                    <h4>Responsive Design</h4>
                                    <p>Ensuring great user experience across all devices</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Projects Section -->
            <section id="projects" class="content-section">
                <h2 class="section-title">Projects</h2>
                <div class="projects-showcase">
                    <div class="project-item">
                        <div class="project-image red">
                            <h3>E-Commerce Platform</h3>
                            <p>Full-stack web application with React & Node.js</p>
                        </div>
                    </div>
                    <div class="project-item">
                        <div class="project-image blue">
                            <h3>Task Management App</h3>
                            <p>React & Firebase collaborative tool</p>
                        </div>
                    </div>
                    <div class="project-item">
                        <div class="project-image purple">
                            <h3>Weather Dashboard</h3>
                            <p>JavaScript API integration project</p>
                        </div>
                    </div>
                </div>
            </section>



            <!-- Stack Section -->
            <section id="stack" class="content-section">
                <h2 class="section-title">Tech Stack</h2>
                <div class="stack-categories">
                    <div class="stack-category">
                        <h3>Frontend</h3>
                        <div class="tech-tags">
                            <span class="tech-tag">JavaScript</span>
                            <span class="tech-tag">Bootstrap</span>
                            <span class="tech-tag">CSS3</span>
                            <span class="tech-tag">HTML5</span>
                        </div>
                    </div>
                    <div class="stack-category">
                        <h3>Backend</h3>
                        <div class="tech-tags">
                            <span class="tech-tag">PHP</span>
                            <span class="tech-tag">MySQL</span>
                        </div>
                    </div>
                              <div class="stack-category">
                        <h3>Languages</h3>
                        <div class="tech-tags">
                            <span class="tech-tag">Language C</span>
                            <span class="tech-tag">Language C#</span>
                            <span class="tech-tag">Java</span>
                        </div>
                    </div>
                    <div class="stack-category">
                        <h3>Tools</h3>
                        <div class="tech-tags">
                            <span class="tech-tag">Git</span>
                            <span class="tech-tag">VS Code</span>
                            <span class="tech-tag">Figma</span>
                            <span class="tech-tag">Linux</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Contact Section -->
            <section id="contact" class="content-section">
                <h2 class="section-title">How to Reach Me</h2>
                <div class="contact-content">
                    <p class="contact-intro">
                        I'm always excited to connect with fellow developers, potential collaborators,
                        and anyone interested in discussing web development projects. Feel free to reach out!
                    </p>

                    <div class="contact-grid">
                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <h3>Email</h3>
                            <p>Drop me a line anytime</p>
                            <a href="mailto:<EMAIL>" class="contact-link">
                                <EMAIL>
                            </a>
                        </div>

                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fab fa-linkedin"></i>
                            </div>
                            <h3>LinkedIn</h3>
                            <p>Let's connect professionally</p>
                            <a href="https://linkedin.com/in/meski-imane" target="_blank" class="contact-link">
                                linkedin.com/in/meski-imane
                            </a>
                        </div>

                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fab fa-github"></i>
                            </div>
                            <h3>GitHub</h3>
                            <p>Check out my code</p>
                            <a href="https://github.com/meski-imane" target="_blank" class="contact-link">
                                github.com/meski-imane
                            </a>
                        </div>

                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fab fa-twitter"></i>
                            </div>
                            <h3>Twitter</h3>
                            <p>Follow my journey</p>
                            <a href="https://twitter.com/meski_imane" target="_blank" class="contact-link">
                                @meski_imane
                            </a>
                        </div>
                    </div>

                    <div class="availability-status">
                        <div class="status-badge">
                            <span class="status-dot"></span>
                            <span>Currently available for freelance projects and full-time opportunities</span>
                        </div>
                        <p class="response-time">I typically respond within 24 hours</p>
                    </div>
                </div>
            </section>
        </main>

        <!-- Theme Toggle -->
        <button class="theme-toggle" id="themeToggle">
            <i class="fas fa-moon"></i>
        </button>
    </div>

    <script src="script.js"></script>
</body>
</html>
