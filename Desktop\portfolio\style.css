/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6b7280;
    --secondary-color: #4b5563;
    --accent-color: #3b82f6;
    --blue-accent: #2563eb;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-sidebar: #ffffff;
    --border-color: #e5e7eb;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --green-dot: #10b981;
    --red-bg: #6b7280;
    --blue-bg: #3b82f6;
    --purple-bg: #374151;
}

[data-theme="dark"] {
    --primary-color: #d1d5db;
    --secondary-color: #f3f4f6;
    --accent-color: #60a5fa;
    --blue-accent: #3b82f6;
    --text-primary: #ffffff;
    --text-secondary: #d1d5db;
    --text-light: #9ca3af;
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-sidebar: #111827;
    --border-color: #374151;
    --red-bg: #d1d5db;
    --blue-bg: #60a5fa;
    --purple-bg: #e5e7eb;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: all 0.3s ease;
}

html {
    scroll-behavior: smooth;
}

.container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background: var(--bg-sidebar);
    border-right: 1px solid var(--border-color);
    padding: 2rem 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    transition: all 0.3s ease;
}

.profile-section {
    text-align: center;
    padding: 0 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.profile-image {
    width: 80px;
    height: 80px;
    background: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 2rem;
}

.profile-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.profile-title {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.sidebar-nav {
    padding: 0 1rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0.25rem;
    text-decoration: none;
    color: var(--text-secondary);
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    font-weight: 500;
}

.nav-item:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.nav-item.active {
    background: var(--accent-color);
    color: white;
}

.nav-item i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    margin-left: 250px;
    padding: 2rem 3rem;
    flex: 1;
    background: var(--bg-primary);
    min-height: 100vh;
}

.content-section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Status Indicator */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--green-dot);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 2rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--green-dot);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Hero Content */
.hero-content {
    margin-bottom: 4rem;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.hero-title .highlight {
    color: var(--accent-color);
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 2rem;
    max-width: 600px;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cta-button:hover {
    border-color: var(--accent-color);
    color: var(--accent-color);
}

/* Recent Projects */
.recent-projects h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.project-card {
    height: 120px;
    border-radius: 0.75rem;
    padding: 1.5rem;
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.project-card:hover {
    transform: translateY(-2px);
}

.project-card.red {
    background: var(--red-bg);
}

.project-card.blue {
    background: var(--blue-bg);
}

.project-card.purple {
    background: var(--purple-bg);
}

.project-content h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.project-content p {
    font-size: 0.8rem;
    opacity: 0.9;
}

/* Section Styles */
.section-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2rem;
}

/* About Section */
.about-content p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.skills-overview {
    margin-top: 3rem;
}

.skills-overview h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.skills-list {
    display: grid;
    gap: 1.5rem;
}

.skill-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
}

.skill-item i {
    font-size: 1.5rem;
    color: var(--accent-color);
    margin-top: 0.25rem;
}

.skill-item h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.skill-item p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Projects Section */
.projects-showcase {
    display: grid;
    gap: 1.5rem;
}

.project-item {
    border-radius: 0.75rem;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.project-image {
    height: 200px;
    padding: 2rem;
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.project-image h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.project-image p {
    font-size: 0.9rem;
    opacity: 0.9;
}



/* Stack Section */
.stack-categories {
    display: grid;
    gap: 2rem;
}

.stack-category h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tech-tag {
    background: var(--accent-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1.5rem;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Contact Section */
.contact-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.contact-methods {
    display: grid;
    gap: 1rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.contact-method:hover {
    border-color: var(--accent-color);
    background: var(--bg-primary);
}

.contact-method i {
    color: var(--accent-color);
    font-size: 1.25rem;
}

/* Theme Toggle */
.theme-toggle {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--accent-color);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    z-index: 1000;
}

.theme-toggle:hover {
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        margin-left: 200px;
        padding: 2rem;
    }

    .sidebar {
        width: 200px;
    }

    .hero-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        z-index: 1000;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }



    .contact-methods {
        gap: 0.75rem;
    }

    .theme-toggle {
        bottom: 1rem;
        right: 1rem;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .project-card {
        height: 100px;
        padding: 1rem;
    }

    .project-content h3 {
        font-size: 0.9rem;
    }

    .project-content p {
        font-size: 0.75rem;
    }
}

/* Mobile Navigation Toggle */
.mobile-toggle {
    display: none;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 0.75rem;
    cursor: pointer;
}

@media (max-width: 768px) {
    .mobile-toggle {
        display: block;
    }
}

/* Smooth transitions */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-name {
        font-size: 2.5rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .profile-pic {
        width: 250px;
        height: 250px;
        font-size: 6rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .stats {
        grid-template-columns: 1fr;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .container {
        padding: 0 1rem;
    }

    section {
        padding: 3rem 0;
    }

    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-name {
        font-size: 2rem;
    }

    .hero-title {
        font-size: 1.2rem;
    }

    .profile-pic {
        width: 200px;
        height: 200px;
        font-size: 4rem;
    }

    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}
